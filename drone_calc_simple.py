# Parameter dasar
poin_awal = 4830
harga_drone = 100
harga_jual_drone = 80
biaya_refresh = 10
drone_per_refresh = 3

# Parameter reward system
batas_ronde = 8000  # Reset ronde jika total akumulasi poin > 8000
reward_milestones = [
    (500, 100),   # 500 poin -> 100 koin
    (1500, 200),  # 1500 poin -> 200 koin
    (2500, 300),  # 2500 poin -> 300 koin
    (3500, 400),  # 3500 poin -> 400 koin
    (5000, 600),  # 5000 poin -> 600 koin
]

# Fungsi untuk menghitung reward berdasarkan akumulasi poin
def hitung_reward(akumulasi_poin):
    total_koin = 0
    for milestone, koin in reward_milestones:
        if akumulasi_poin >= milestone:
            total_koin = koin
        else:
            break
    return total_koin

# Simulasi multi-ronde dengan tracking total poin dipakai
total_ronde_selesai = 0
total_koin_reward = 0
total_poin_dipakai_semua = 0
poin_tersisa_global = poin_awal

print("=== SIMULASI REWARD MULTI-RONDE ===")
print(f"Poin awal: {poin_awal}")
print(f"Batas ronde: {batas_ronde} poin")

# Estimasi berapa ronde yang bisa diselesaikan
estimasi_total_poin = poin_awal * 1.5  # Asumsi dengan jual-beli bisa 1.5x lipat
estimasi_ronde = estimasi_total_poin / batas_ronde
print(f"Estimasi ronde yang bisa diselesaikan: {estimasi_ronde:.1f}")

ronde_ke = 0
while poin_tersisa_global >= (biaya_refresh + harga_drone):
    ronde_ke += 1
    print(f"\nRONDE {ronde_ke}")
    print(f"Poin awal ronde: {poin_tersisa_global}")

    # Simulasi dengan mekanisme jual-beli yang optimal
    akumulasi_poin_ronde = 0
    poin_tersisa = poin_tersisa_global
    total_drone_dimiliki = 0
    poin_awal_ronde = poin_tersisa_global

    # Fase 1: Hari 1-6 (18 refresh gratis)
    refresh_gratis = 18
    for i in range(refresh_gratis):
        if akumulasi_poin_ronde >= batas_ronde:
            break

        # Beli drone dari refresh gratis
        drone_bisa_beli = min(3, (batas_ronde - akumulasi_poin_ronde) // harga_drone)
        if drone_bisa_beli > 0 and poin_tersisa >= (drone_bisa_beli * harga_drone):
            poin_tersisa -= (drone_bisa_beli * harga_drone)
            akumulasi_poin_ronde += (drone_bisa_beli * harga_drone)
            total_drone_dimiliki += drone_bisa_beli
        elif total_drone_dimiliki > 0:
            # Jual drone untuk mendapatkan poin
            poin_jual = total_drone_dimiliki * harga_jual_drone
            poin_tersisa += poin_jual
            total_drone_dimiliki = 0

            # Coba beli lagi
            drone_bisa_beli = min(3, (batas_ronde - akumulasi_poin_ronde) // harga_drone)
            if drone_bisa_beli > 0 and poin_tersisa >= (drone_bisa_beli * harga_drone):
                poin_tersisa -= (drone_bisa_beli * harga_drone)
                akumulasi_poin_ronde += (drone_bisa_beli * harga_drone)
                total_drone_dimiliki += drone_bisa_beli

    # Fase 2: Hari 7 (3 refresh gratis + refresh berbayar)
    refresh_gratis_hari7 = 3
    for i in range(refresh_gratis_hari7):
        if akumulasi_poin_ronde >= batas_ronde:
            break

        # Beli drone dari refresh gratis hari 7
        drone_bisa_beli = min(3, (batas_ronde - akumulasi_poin_ronde) // harga_drone)
        if drone_bisa_beli > 0 and poin_tersisa >= (drone_bisa_beli * harga_drone):
            poin_tersisa -= (drone_bisa_beli * harga_drone)
            akumulasi_poin_ronde += (drone_bisa_beli * harga_drone)
            total_drone_dimiliki += drone_bisa_beli
        elif total_drone_dimiliki > 0:
            # Jual drone untuk mendapatkan poin
            poin_jual = total_drone_dimiliki * harga_jual_drone
            poin_tersisa += poin_jual
            total_drone_dimiliki = 0

            # Coba beli lagi
            drone_bisa_beli = min(3, (batas_ronde - akumulasi_poin_ronde) // harga_drone)
            if drone_bisa_beli > 0 and poin_tersisa >= (drone_bisa_beli * harga_drone):
                poin_tersisa -= (drone_bisa_beli * harga_drone)
                akumulasi_poin_ronde += (drone_bisa_beli * harga_drone)
                total_drone_dimiliki += drone_bisa_beli

    # Fase 3: Refresh berbayar sampai batas ronde
    while poin_tersisa >= (biaya_refresh + harga_drone) and akumulasi_poin_ronde < batas_ronde:
        # Cek apakah masih bisa bayar refresh
        if akumulasi_poin_ronde + biaya_refresh >= batas_ronde:
            break

        # Bayar refresh
        poin_tersisa -= biaya_refresh
        akumulasi_poin_ronde += biaya_refresh

        # Beli drone dari refresh berbayar
        drone_bisa_beli = min(3, (batas_ronde - akumulasi_poin_ronde) // harga_drone)
        if drone_bisa_beli > 0 and poin_tersisa >= (drone_bisa_beli * harga_drone):
            poin_tersisa -= (drone_bisa_beli * harga_drone)
            akumulasi_poin_ronde += (drone_bisa_beli * harga_drone)
            total_drone_dimiliki += drone_bisa_beli
        elif total_drone_dimiliki > 0:
            # Jual drone untuk mendapatkan poin
            poin_jual = total_drone_dimiliki * harga_jual_drone
            poin_tersisa += poin_jual
            total_drone_dimiliki = 0

            # Coba beli lagi
            drone_bisa_beli = min(3, (batas_ronde - akumulasi_poin_ronde) // harga_drone)
            if drone_bisa_beli > 0 and poin_tersisa >= (drone_bisa_beli * harga_drone):
                poin_tersisa -= (drone_bisa_beli * harga_drone)
                akumulasi_poin_ronde += (drone_bisa_beli * harga_drone)
                total_drone_dimiliki += drone_bisa_beli
        else:
            break
    
    # Hitung total poin yang benar-benar dipakai di ronde ini
    poin_dipakai_ronde = poin_awal_ronde - poin_tersisa
    total_poin_dipakai_semua += poin_dipakai_ronde

    # Hitung reward untuk ronde ini
    koin_reward_ronde = hitung_reward(akumulasi_poin_ronde)

    print(f"Akumulasi poin ronde: {akumulasi_poin_ronde}")
    print(f"Poin dipakai ronde ini: {poin_dipakai_ronde}")
    print(f"Koin reward ronde: {koin_reward_ronde}")
    print(f"Drone akhir di tangan: {total_drone_dimiliki}")
    print(f"Sisa poin: {poin_tersisa}")

    # Cek apakah ronde selesai (mencapai batas 8000)
    if akumulasi_poin_ronde >= batas_ronde:
        total_ronde_selesai += 1
        total_koin_reward += koin_reward_ronde
        print(f"✅ RONDE {ronde_ke} SELESAI!")
    else:
        # Ronde tidak selesai, hitung progress
        progress_persen = (akumulasi_poin_ronde / batas_ronde) * 100
        print(f"❌ RONDE {ronde_ke} TIDAK SELESAI ({progress_persen:.1f}% dari target)")
        print(f"Target: {batas_ronde}, Tercapai: {akumulasi_poin_ronde}")

        # Tampilkan reward parsial jika ada
        if koin_reward_ronde > 0:
            total_koin_reward += koin_reward_ronde
            print(f"Reward parsial: {koin_reward_ronde} koin")

    # Update poin untuk ronde berikutnya (jual semua drone yang tersisa)
    if total_drone_dimiliki > 0:
        poin_jual_akhir = total_drone_dimiliki * harga_jual_drone
        poin_tersisa += poin_jual_akhir
        print(f"Jual {total_drone_dimiliki} drone tersisa: +{poin_jual_akhir} poin")

    poin_tersisa_global = poin_tersisa

    # Jika tidak bisa lanjut ronde berikutnya
    if poin_tersisa_global < (biaya_refresh + harga_drone):
        print(f"Tidak bisa lanjut ronde berikutnya (poin tidak cukup)")
        break

# Hitung estimasi ronde berdasarkan total poin dipakai
estimasi_ronde_akurat = total_poin_dipakai_semua / batas_ronde

print(f"\n{'='*60}")
print("HASIL AKHIR")
print(f"{'='*60}")
print(f"Poin awal: {poin_awal}")
print(f"Total poin yang dipakai: {total_poin_dipakai_semua}")
print(f"Estimasi ronde berdasarkan poin dipakai: {estimasi_ronde_akurat:.2f}")
print(f"Ronde yang berhasil diselesaikan: {total_ronde_selesai}")
print(f"Total ronde yang dimainkan: {ronde_ke}")
print(f"Total koin reward yang didapat: {total_koin_reward}")
print(f"Sisa poin akhir: {poin_tersisa_global}")

print(f"\n=== DETAIL REWARD PER MILESTONE ===")
for milestone, koin in reward_milestones:
    print(f"Milestone {milestone} poin -> {koin} koin")

print(f"\n=== KESIMPULAN ===")
print(f"Dengan poin awal {poin_awal}:")
print(f"✅ Total poin yang bisa dipakai: {total_poin_dipakai_semua}")
print(f"✅ Estimasi ronde: {estimasi_ronde_akurat:.2f} ronde")
print(f"✅ Ronde selesai: {total_ronde_selesai} ronde")
print(f"✅ Total koin reward: {total_koin_reward} koin")

if estimasi_ronde_akurat < 3:
    ronde_tidak_selesai = int(estimasi_ronde_akurat) + 1
    progress_ronde_terakhir = (estimasi_ronde_akurat % 1) * 100
    print(f"⚠️  Ronde ke-{ronde_tidak_selesai} tidak selesai ({progress_ronde_terakhir:.1f}% progress)")

    # Hitung reward milestone yang tercapai di ronde tidak selesai
    poin_ronde_terakhir = (estimasi_ronde_akurat % 1) * batas_ronde
    reward_parsial = hitung_reward(poin_ronde_terakhir)
    if reward_parsial > 0:
        print(f"💰 Reward parsial ronde ke-{ronde_tidak_selesai}: {reward_parsial} koin")
