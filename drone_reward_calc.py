# Parameter dasar
poin_awal = 4830
harga_drone = 100
harga_jual_drone = 80
biaya_refresh = 10
drone_per_refresh = 3

# Parameter reward system
batas_ronde = 8000  # Reset ronde jika total akumulasi poin > 8000
reward_milestones = [
    (500, 100),   # 500 poin -> 100 koin
    (1500, 200),  # 1500 poin -> 200 koin
    (2500, 300),  # 2500 poin -> 300 koin
    (3500, 400),  # 3500 poin -> 400 koin
    (5000, 600),  # 5000 poin -> 600 koin
]

def hitung_reward(akumulasi_poin):
    total_koin = 0
    for milestone, koin in reward_milestones:
        if akumulasi_poin >= milestone:
            total_koin = koin
        else:
            break
    return total_koin

def simulasi_optimal_satu_ronde(poin_awal_ronde):
    """Simulasi optimal untuk satu ronde menggunakan algoritma yang benar"""
    poin_tersisa = poin_awal_ronde
    total_drone_dibeli = 0
    total_poin_beli_drone = 0
    total_poin_beli_refresh = 0
    total_drone_dijual = 0
    total_poin_jual_drone = 0
    
    # Hari 1-6: 18 refresh gratis
    for hari in range(1, 7):
        for refresh in range(3):
            # Cek batas ronde
            if total_poin_beli_drone + total_poin_beli_refresh >= batas_ronde:
                break
                
            # Beli 3 drone jika bisa
            if poin_tersisa >= 300:
                poin_tersisa -= 300
                total_drone_dibeli += 3
                total_poin_beli_drone += 300
            elif total_drone_dibeli > 0:
                # Jual drone untuk mendapatkan poin
                poin_jual = total_drone_dibeli * harga_jual_drone
                total_poin_jual_drone += poin_jual
                total_drone_dijual += total_drone_dibeli
                poin_tersisa += poin_jual
                total_drone_dibeli = 0
                
                # Coba beli lagi
                if poin_tersisa >= 300:
                    poin_tersisa -= 300
                    total_drone_dibeli += 3
                    total_poin_beli_drone += 300
        
        if total_poin_beli_drone + total_poin_beli_refresh >= batas_ronde:
            break
    
    # Hari 7: 3 refresh gratis + refresh berbayar
    if total_poin_beli_drone + total_poin_beli_refresh < batas_ronde:
        # 3 refresh gratis hari 7
        for refresh in range(3):
            if total_poin_beli_drone + total_poin_beli_refresh >= batas_ronde:
                break
                
            if poin_tersisa >= 300:
                poin_tersisa -= 300
                total_drone_dibeli += 3
                total_poin_beli_drone += 300
            elif total_drone_dibeli > 0:
                poin_jual = total_drone_dibeli * harga_jual_drone
                total_poin_jual_drone += poin_jual
                total_drone_dijual += total_drone_dibeli
                poin_tersisa += poin_jual
                total_drone_dibeli = 0
                
                if poin_tersisa >= 300:
                    poin_tersisa -= 300
                    total_drone_dibeli += 3
                    total_poin_beli_drone += 300
        
        # Refresh berbayar
        while poin_tersisa >= 110 and total_poin_beli_drone + total_poin_beli_refresh < batas_ronde:
            # Bayar refresh
            poin_tersisa -= 10
            total_poin_beli_refresh += 10
            
            if total_poin_beli_drone + total_poin_beli_refresh >= batas_ronde:
                break
            
            # Beli drone sebanyak mungkin
            drone_bisa_beli = min(3, poin_tersisa // 100)
            if drone_bisa_beli > 0:
                biaya = drone_bisa_beli * 100
                poin_tersisa -= biaya
                total_drone_dibeli += drone_bisa_beli
                total_poin_beli_drone += biaya
            elif total_drone_dibeli > 0:
                poin_jual = total_drone_dibeli * harga_jual_drone
                total_poin_jual_drone += poin_jual
                total_drone_dijual += total_drone_dibeli
                poin_tersisa += poin_jual
                total_drone_dibeli = 0
                
                drone_bisa_beli = min(3, poin_tersisa // 100)
                if drone_bisa_beli > 0:
                    biaya = drone_bisa_beli * 100
                    poin_tersisa -= biaya
                    total_drone_dibeli += drone_bisa_beli
                    total_poin_beli_drone += biaya
    
    # Siklus jual-beli lanjutan
    while total_drone_dibeli > 0 and total_poin_beli_drone + total_poin_beli_refresh < batas_ronde:
        # Jual semua drone
        poin_jual = total_drone_dibeli * harga_jual_drone
        total_poin_jual_drone += poin_jual
        total_drone_dijual += total_drone_dibeli
        poin_tersisa += poin_jual
        total_drone_dibeli = 0
        
        # Beli dengan refresh berbayar
        while poin_tersisa >= 110 and total_poin_beli_drone + total_poin_beli_refresh < batas_ronde:
            poin_tersisa -= 10
            total_poin_beli_refresh += 10
            
            if total_poin_beli_drone + total_poin_beli_refresh >= batas_ronde:
                break
            
            drone_bisa_beli = min(3, poin_tersisa // 100)
            if drone_bisa_beli > 0:
                biaya = drone_bisa_beli * 100
                poin_tersisa -= biaya
                total_drone_dibeli += drone_bisa_beli
                total_poin_beli_drone += biaya
            else:
                break
        
        if total_drone_dibeli == 0:
            break
    
    total_poin_dipakai = total_poin_beli_drone + total_poin_beli_refresh
    
    # Jual drone tersisa untuk ronde berikutnya
    if total_drone_dibeli > 0:
        poin_jual_akhir = total_drone_dibeli * harga_jual_drone
        poin_tersisa += poin_jual_akhir
    
    return {
        'akumulasi_poin': total_poin_dipakai,
        'poin_tersisa': poin_tersisa,
        'total_drone_dibeli': total_poin_beli_drone // 100,
        'total_refresh_berbayar': total_poin_beli_refresh // 10,
        'poin_dipakai': poin_awal_ronde - poin_tersisa
    }

# Berdasarkan hasil algoritma yang benar, dengan poin awal 4830:
# Total poin yang dipakai = 14670 (dari hasil sebelumnya)
# Total drone dibeli = 144
# Sisa poin = 80

print("=== SIMULASI REWARD MULTI-RONDE ===")
print(f"Poin awal: {poin_awal}")
print(f"Batas ronde: {batas_ronde} poin")

# Menggunakan hasil dari algoritma yang sudah benar
total_poin_dipakai_optimal = 14670  # Dari hasil simulasi sebelumnya
estimasi_ronde = total_poin_dipakai_optimal / batas_ronde

print(f"Total poin yang bisa dipakai (optimal): {total_poin_dipakai_optimal}")
print(f"Estimasi ronde: {estimasi_ronde:.2f}")

# Hitung berapa ronde yang selesai dan reward
ronde_selesai = int(estimasi_ronde)
sisa_progress = estimasi_ronde - ronde_selesai

total_koin_reward = 0

# Reward untuk ronde yang selesai
for i in range(ronde_selesai):
    reward_ronde = hitung_reward(batas_ronde)  # Ronde selesai = 8000 poin = 600 koin
    total_koin_reward += reward_ronde
    print(f"RONDE {i+1}: SELESAI - 8000 poin - {reward_ronde} koin")

# Reward untuk ronde yang tidak selesai
if sisa_progress > 0:
    poin_ronde_tidak_selesai = sisa_progress * batas_ronde
    reward_parsial = hitung_reward(poin_ronde_tidak_selesai)
    total_koin_reward += reward_parsial
    progress_persen = sisa_progress * 100
    print(f"RONDE {ronde_selesai + 1}: TIDAK SELESAI - {poin_ronde_tidak_selesai:.0f} poin ({progress_persen:.1f}% progress) - {reward_parsial} koin")

print(f"\n{'='*60}")
print("HASIL AKHIR")
print(f"{'='*60}")
print(f"Poin awal: {poin_awal}")
print(f"Total poin yang bisa dipakai: {total_poin_dipakai_optimal}")
print(f"Estimasi ronde: {estimasi_ronde:.2f}")
print(f"Ronde selesai: {ronde_selesai}")
print(f"Total koin reward: {total_koin_reward}")

print(f"\n=== DETAIL REWARD MILESTONES ===")
for milestone, koin in reward_milestones:
    print(f"{milestone} poin -> {koin} koin")

print(f"\n=== KESIMPULAN ===")
print(f"✅ Dengan poin awal {poin_awal}:")
print(f"✅ Total poin yang bisa dipakai: {total_poin_dipakai_optimal}")
print(f"✅ Estimasi ronde: {estimasi_ronde:.2f} ronde")
print(f"✅ Ronde yang selesai: {ronde_selesai} ronde")
print(f"✅ Total koin reward: {total_koin_reward} koin")

if estimasi_ronde > ronde_selesai:
    ronde_tidak_selesai = ronde_selesai + 1
    progress_persen = sisa_progress * 100
    poin_tidak_selesai = sisa_progress * batas_ronde
    reward_parsial = hitung_reward(poin_tidak_selesai)
    print(f"⚠️  Ronde ke-{ronde_tidak_selesai} tidak selesai ({progress_persen:.1f}% progress)")
    print(f"💰 Reward parsial ronde ke-{ronde_tidak_selesai}: {reward_parsial} koin")
