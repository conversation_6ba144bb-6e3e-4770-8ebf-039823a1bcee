# Parameter dasar
# poin_awal = 3230
poin_awal = 4830
harga_drone = 100
harga_jual_drone = 80
biaya_refresh = 10
drone_per_refresh = 3

# Parameter reward system
batas_ronde = 8000  # Reset ronde jika total akumulasi poin > 8000
reward_milestones = [
    (500, 100),   # 500 poin -> 100 koin
    (1500, 200),  # 1500 poin -> 200 koin
    (2500, 300),  # 2500 poin -> 300 koin
    (3500, 400),  # 3500 poin -> 400 koin
    (5000, 600),  # 5000 poin -> 600 koin
]

# Fungsi untuk menghitung reward berdasarkan akumulasi poin
def hitung_reward(akumulasi_poin):
    total_koin = 0
    for milestone, koin in reward_milestones:
        if akumulasi_poin >= milestone:
            total_koin = koin
        else:
            break
    return total_koin

# Simulasi multi-ronde
total_ronde = 0
total_koin_reward = 0
poin_tersisa_global = poin_awal

print("=== SIMULASI MULTI-RONDE ===")
print(f"Poin awal: {poin_awal}")
print(f"Batas ronde: {batas_ronde} poin")
print(f"Reward milestones: {reward_milestones}")

while poin_tersisa_global > 0:
    total_ronde += 1
    print(f"\n{'='*50}")
    print(f"RONDE {total_ronde}")
    print(f"{'='*50}")
    print(f"Poin awal ronde: {poin_tersisa_global}")

    # Reset variabel untuk ronde baru
    poin_tersisa = poin_tersisa_global
    total_drone_dibeli = 0
    total_poin_beli_drone = 0
    total_poin_beli_refresh = 0
    total_drone_dijual = 0
    total_poin_jual_drone = 0
    akumulasi_poin_ronde = 0

def beli_dari_refresh(drone_tersedia_refresh, nama_refresh):
    """Beli drone dari refresh yang tersedia sampai habis atau poin tidak cukup"""
    global poin_tersisa, total_drone_dibeli, total_poin_beli_drone, akumulasi_poin_ronde

    drone_dibeli_refresh = 0
    while drone_tersedia_refresh > 0 and poin_tersisa >= harga_drone and akumulasi_poin_ronde < batas_ronde:
        poin_tersisa -= harga_drone
        total_drone_dibeli += 1
        total_poin_beli_drone += harga_drone
        akumulasi_poin_ronde += harga_drone
        drone_tersedia_refresh -= 1
        drone_dibeli_refresh += 1

        # Cek apakah sudah mencapai batas ronde
        if akumulasi_poin_ronde >= batas_ronde:
            print(f"    {nama_refresh}: Beli {drone_dibeli_refresh} drone (-{drone_dibeli_refresh * harga_drone} poin), sisa: {poin_tersisa}")
            print(f"    *** BATAS RONDE TERCAPAI ({akumulasi_poin_ronde} >= {batas_ronde}) ***")
            return drone_tersedia_refresh

    if drone_dibeli_refresh > 0:
        print(f"    {nama_refresh}: Beli {drone_dibeli_refresh} drone (-{drone_dibeli_refresh * harga_drone} poin), sisa: {poin_tersisa}")
        if drone_tersedia_refresh > 0 and akumulasi_poin_ronde < batas_ronde:
            print(f"      Masih ada {drone_tersedia_refresh} drone tersisa di refresh ini (poin tidak cukup)")

    return drone_tersedia_refresh

def jual_semua_drone():
    """Jual semua drone yang dimiliki"""
    global poin_tersisa, total_drone_dibeli, total_drone_dijual, total_poin_jual_drone

    if total_drone_dibeli > 0:
        poin_jual = total_drone_dibeli * harga_jual_drone
        total_poin_jual_drone += poin_jual
        total_drone_dijual += total_drone_dibeli
        poin_tersisa += poin_jual
        print(f"    Jual {total_drone_dibeli} drone (+{poin_jual} poin), sisa: {poin_tersisa}")
        total_drone_dibeli = 0

    # Hari 1-6: 3x free refresh per hari
    for hari in range(1, 7):
        if akumulasi_poin_ronde >= batas_ronde:
            break

        print(f"\nHari {hari}:")
        print(f"  Poin awal hari: {poin_tersisa}")
        print(f"  Akumulasi poin ronde: {akumulasi_poin_ronde}")

        # 3 refresh gratis per hari
        for refresh in range(3):
            if akumulasi_poin_ronde >= batas_ronde:
                break

            drone_tersedia = 3  # Setiap refresh menyediakan 3 drone

            # Coba beli dari refresh
            drone_tersisa = beli_dari_refresh(drone_tersedia, f"Refresh gratis {refresh+1}")

            # Jika tidak bisa beli semua drone dari refresh dan masih ada drone tersisa
            if drone_tersisa > 0 and poin_tersisa < harga_drone and akumulasi_poin_ronde < batas_ronde:
                # Jual drone untuk mendapatkan poin
                jual_semua_drone()

                # Coba beli lagi dari refresh yang sama
                if poin_tersisa >= harga_drone and akumulasi_poin_ronde < batas_ronde:
                    drone_tersisa = beli_dari_refresh(drone_tersisa, f"Refresh gratis {refresh+1} (setelah jual)")

    # Hari 7: 3x free refresh + refresh berbayar
    if akumulasi_poin_ronde < batas_ronde:
        print(f"\nHari 7:")
        print(f"  Poin awal hari: {poin_tersisa}")
        print(f"  Akumulasi poin ronde: {akumulasi_poin_ronde}")

        # 3 refresh gratis di hari 7
        for refresh in range(3):
            if akumulasi_poin_ronde >= batas_ronde:
                break

            drone_tersedia = 3  # Setiap refresh menyediakan 3 drone

            # Coba beli dari refresh
            drone_tersisa = beli_dari_refresh(drone_tersedia, f"Refresh gratis {refresh+1}")

            # Jika tidak bisa beli semua drone dari refresh dan masih ada drone tersisa
            if drone_tersisa > 0 and poin_tersisa < harga_drone and akumulasi_poin_ronde < batas_ronde:
                # Jual drone untuk mendapatkan poin
                jual_semua_drone()

                # Coba beli lagi dari refresh yang sama
                if poin_tersisa >= harga_drone and akumulasi_poin_ronde < batas_ronde:
                    drone_tersisa = beli_dari_refresh(drone_tersisa, f"Refresh gratis {refresh+1} (setelah jual)")

        # Refresh berbayar di hari 7
        refresh_berbayar = 0
        while poin_tersisa >= (biaya_refresh + harga_drone) and akumulasi_poin_ronde < batas_ronde:  # Minimal bisa beli 1 drone
            # Bayar refresh
            poin_tersisa -= biaya_refresh
            total_poin_beli_refresh += biaya_refresh
            akumulasi_poin_ronde += biaya_refresh
            refresh_berbayar += 1

            # Cek apakah sudah mencapai batas ronde setelah bayar refresh
            if akumulasi_poin_ronde >= batas_ronde:
                print(f"    Refresh berbayar {refresh_berbayar}: Bayar refresh (-{biaya_refresh}), sisa: {poin_tersisa}")
                print(f"    *** BATAS RONDE TERCAPAI ({akumulasi_poin_ronde} >= {batas_ronde}) ***")
                break

            drone_tersedia = 3  # Setiap refresh berbayar menyediakan 3 drone
            print(f"    Refresh berbayar {refresh_berbayar}: Bayar refresh (-{biaya_refresh}), sisa: {poin_tersisa}")

            # Beli drone dari refresh sampai habis atau poin tidak cukup
            drone_tersisa = beli_dari_refresh(drone_tersedia, f"      Beli dari refresh {refresh_berbayar}")

            # Jika masih ada drone tersisa di refresh dan poin tidak cukup, jual drone
            while drone_tersisa > 0 and poin_tersisa < harga_drone and total_drone_dibeli > 0 and akumulasi_poin_ronde < batas_ronde:
                jual_semua_drone()
                if poin_tersisa >= harga_drone and akumulasi_poin_ronde < batas_ronde:
                    drone_tersisa = beli_dari_refresh(drone_tersisa, f"      Beli lagi dari refresh {refresh_berbayar} (setelah jual)")

    # Lanjutkan jual-beli sampai tidak bisa lagi
    if akumulasi_poin_ronde < batas_ronde and total_drone_dibeli > 0:
        print(f"\n=== SIKLUS JUAL-BELI LANJUTAN ===")
        siklus = 1

        while total_drone_dibeli > 0 and akumulasi_poin_ronde < batas_ronde:
            # Jual semua drone yang ada
            print(f"Siklus {siklus}:")
            jual_semua_drone()

            # Coba beli drone dengan refresh berbayar
            drone_beli_siklus = 0
            refresh_siklus = 0

            while poin_tersisa >= (biaya_refresh + harga_drone) and akumulasi_poin_ronde < batas_ronde:  # Minimal bisa beli 1 drone
                # Bayar refresh
                poin_tersisa -= biaya_refresh
                total_poin_beli_refresh += biaya_refresh
                akumulasi_poin_ronde += biaya_refresh
                refresh_siklus += 1

                # Cek apakah sudah mencapai batas ronde setelah bayar refresh
                if akumulasi_poin_ronde >= batas_ronde:
                    print(f"  Refresh berbayar {refresh_siklus}: Bayar refresh (-{biaya_refresh}), sisa: {poin_tersisa}")
                    print(f"  *** BATAS RONDE TERCAPAI ({akumulasi_poin_ronde} >= {batas_ronde}) ***")
                    break

                drone_tersedia = 3  # Setiap refresh berbayar menyediakan 3 drone
                print(f"  Refresh berbayar {refresh_siklus}: Bayar refresh (-{biaya_refresh}), sisa: {poin_tersisa}")

                # Beli drone dari refresh sampai habis atau poin tidak cukup
                drone_tersisa = beli_dari_refresh(drone_tersedia, f"    Beli dari refresh {refresh_siklus}")
                drone_beli_refresh = 3 - drone_tersisa
                drone_beli_siklus += drone_beli_refresh

                # Jika masih ada drone tersisa di refresh dan poin tidak cukup, jual drone
                while drone_tersisa > 0 and poin_tersisa < harga_drone and total_drone_dibeli > 0 and akumulasi_poin_ronde < batas_ronde:
                    jual_semua_drone()
                    if poin_tersisa >= harga_drone and akumulasi_poin_ronde < batas_ronde:
                        drone_tersisa_baru = beli_dari_refresh(drone_tersisa, f"    Beli lagi dari refresh {refresh_siklus} (setelah jual)")
                        drone_beli_refresh += (drone_tersisa - drone_tersisa_baru)
                        drone_beli_siklus += (drone_tersisa - drone_tersisa_baru)
                        drone_tersisa = drone_tersisa_baru

            if drone_beli_siklus > 0:
                print(f"  Total beli {drone_beli_siklus} drone dengan {refresh_siklus} refresh berbayar, sisa: {poin_tersisa}")
                siklus += 1
            else:
                print(f"  Tidak bisa beli drone lagi (poin tidak cukup untuk refresh + 1 drone)")
                break

    print(f"\n=== RINGKASAN SIMULASI RONDE {total_ronde} ===")
    print(f"Total drone dibeli: {total_drone_dibeli}")
    print(f"Total drone dijual: {total_drone_dijual}")
    print(f"Total poin beli drone: {total_poin_beli_drone}")
    print(f"Total poin beli refresh: {total_poin_beli_refresh}")
    print(f"Total poin jual drone: {total_poin_jual_drone}")
    print(f"Sisa poin akhir: {poin_tersisa}")

    # Hitung total poin yang dipakai dari simulasi ronde ini
    total_poin_dipakai_ronde = total_poin_beli_drone + total_poin_beli_refresh

    # Hitung reward untuk ronde ini
    koin_reward_ronde = hitung_reward(total_poin_dipakai_ronde)
    total_koin_reward += koin_reward_ronde

    print(f"\n=== HASIL RONDE {total_ronde} ===")
    print(f"Total poin yang dipakai ronde ini: {total_poin_dipakai_ronde}")
    print(f"  - Pembelian drone: {total_poin_beli_drone}")
    print(f"  - Pembelian refresh: {total_poin_beli_refresh}")
    print(f"Koin reward ronde ini: {koin_reward_ronde}")
    print(f"Sisa poin akhir ronde: {poin_tersisa}")
    print(f"Total drone akhir di tangan: {total_drone_dibeli}")
    print(f"Total drone yang pernah dijual: {total_drone_dijual}")

    # Update poin tersisa global untuk ronde berikutnya
    poin_tersisa_global = poin_tersisa

    # Jika masih ada drone di tangan, jual semua untuk ronde berikutnya
    if total_drone_dibeli > 0:
        poin_jual_akhir = total_drone_dibeli * harga_jual_drone
        poin_tersisa_global += poin_jual_akhir
        print(f"Jual {total_drone_dibeli} drone tersisa untuk ronde berikutnya: +{poin_jual_akhir} poin")
        print(f"Poin untuk ronde berikutnya: {poin_tersisa_global}")

    # Cek apakah masih bisa lanjut ronde berikutnya
    if poin_tersisa_global < (biaya_refresh + harga_drone):
        print(f"Tidak bisa lanjut ronde berikutnya (poin tidak cukup: {poin_tersisa_global} < {biaya_refresh + harga_drone})")
        break

print(f"\n{'='*60}")
print("RINGKASAN SEMUA RONDE")
print(f"{'='*60}")
print(f"Total ronde yang dimainkan: {total_ronde}")
print(f"Total koin reward yang didapat: {total_koin_reward}")
print(f"Sisa poin akhir: {poin_tersisa_global}")

print(f"\n=== DETAIL REWARD PER MILESTONE ===")
for milestone, koin in reward_milestones:
    print(f"Milestone {milestone} poin -> {koin} koin")

print(f"\n=== RATA-RATA PER RONDE ===")
if total_ronde > 0:
    print(f"Rata-rata koin reward per ronde: {total_koin_reward / total_ronde:.1f}")
    print(f"Rata-rata poin dipakai per ronde: {(poin_awal - poin_tersisa_global) / total_ronde:.1f}")