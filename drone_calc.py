# Parameter dasar
# poin_awal = 3230
poin_awal = 4830
harga_drone = 100
harga_jual_drone = 80
biaya_refresh = 10
drone_per_refresh = 3

# Simulasi hari per hari dengan jual-beli otomatis
poin_tersisa = poin_awal
total_drone_dibeli = 0
total_poin_beli_drone = 0
total_poin_beli_refresh = 0
total_drone_dijual = 0
total_poin_jual_drone = 0

print("=== SIMULASI HARI PER HARI ===")

def beli_dari_refresh(drone_tersedia_refresh, nama_refresh):
    """Beli drone dari refresh yang tersedia sampai habis atau poin tidak cukup"""
    global poin_tersisa, total_drone_dibeli, total_poin_beli_drone

    drone_dibeli_refresh = 0
    while drone_tersedia_refresh > 0 and poin_tersisa >= harga_drone:
        poin_tersisa -= harga_drone
        total_drone_dibeli += 1
        total_poin_beli_drone += harga_drone
        drone_tersedia_refresh -= 1
        drone_dibeli_refresh += 1

    if drone_dibeli_refresh > 0:
        print(f"    {nama_refresh}: Beli {drone_dibeli_refresh} drone (-{drone_dibeli_refresh * harga_drone} poin), sisa: {poin_tersisa}")
        if drone_tersedia_refresh > 0:
            print(f"      Masih ada {drone_tersedia_refresh} drone tersisa di refresh ini (poin tidak cukup)")

    return drone_tersedia_refresh

def jual_semua_drone():
    """Jual semua drone yang dimiliki"""
    global poin_tersisa, total_drone_dibeli, total_drone_dijual, total_poin_jual_drone

    if total_drone_dibeli > 0:
        poin_jual = total_drone_dibeli * harga_jual_drone
        total_poin_jual_drone += poin_jual
        total_drone_dijual += total_drone_dibeli
        poin_tersisa += poin_jual
        print(f"    Jual {total_drone_dibeli} drone (+{poin_jual} poin), sisa: {poin_tersisa}")
        total_drone_dibeli = 0

# Hari 1-6: 3x free refresh per hari
for hari in range(1, 7):
    print(f"\nHari {hari}:")
    print(f"  Poin awal hari: {poin_tersisa}")

    # 3 refresh gratis per hari
    for refresh in range(3):
        drone_tersedia = 3  # Setiap refresh menyediakan 3 drone

        # Coba beli dari refresh
        drone_tersisa = beli_dari_refresh(drone_tersedia, f"Refresh gratis {refresh+1}")

        # Jika tidak bisa beli semua drone dari refresh dan masih ada drone tersisa
        if drone_tersisa > 0 and poin_tersisa < harga_drone:
            # Jual drone untuk mendapatkan poin
            jual_semua_drone()

            # Coba beli lagi dari refresh yang sama
            if poin_tersisa >= harga_drone:
                drone_tersisa = beli_dari_refresh(drone_tersisa, f"Refresh gratis {refresh+1} (setelah jual)")

# Hari 7: 3x free refresh + refresh berbayar
print(f"\nHari 7:")
print(f"  Poin awal hari: {poin_tersisa}")

# 3 refresh gratis di hari 7
for refresh in range(3):
    drone_tersedia = 3  # Setiap refresh menyediakan 3 drone

    # Coba beli dari refresh
    drone_tersisa = beli_dari_refresh(drone_tersedia, f"Refresh gratis {refresh+1}")

    # Jika tidak bisa beli semua drone dari refresh dan masih ada drone tersisa
    if drone_tersisa > 0 and poin_tersisa < harga_drone:
        # Jual drone untuk mendapatkan poin
        jual_semua_drone()

        # Coba beli lagi dari refresh yang sama
        if poin_tersisa >= harga_drone:
            drone_tersisa = beli_dari_refresh(drone_tersisa, f"Refresh gratis {refresh+1} (setelah jual)")

# Refresh berbayar di hari 7
refresh_berbayar = 0
while poin_tersisa >= (biaya_refresh + harga_drone):  # Minimal bisa beli 1 drone
    # Bayar refresh
    poin_tersisa -= biaya_refresh
    total_poin_beli_refresh += biaya_refresh
    refresh_berbayar += 1

    drone_tersedia = 3  # Setiap refresh berbayar menyediakan 3 drone
    print(f"    Refresh berbayar {refresh_berbayar}: Bayar refresh (-{biaya_refresh}), sisa: {poin_tersisa}")

    # Beli drone dari refresh sampai habis atau poin tidak cukup
    drone_tersisa = beli_dari_refresh(drone_tersedia, f"      Beli dari refresh {refresh_berbayar}")

    # Jika masih ada drone tersisa di refresh dan poin tidak cukup, jual drone
    while drone_tersisa > 0 and poin_tersisa < harga_drone and total_drone_dibeli > 0:
        jual_semua_drone()
        if poin_tersisa >= harga_drone:
            drone_tersisa = beli_dari_refresh(drone_tersisa, f"      Beli lagi dari refresh {refresh_berbayar} (setelah jual)")

# Lanjutkan jual-beli sampai tidak bisa lagi
print(f"\n=== SIKLUS JUAL-BELI LANJUTAN ===")
siklus = 1

while total_drone_dibeli > 0:
    # Jual semua drone yang ada
    print(f"Siklus {siklus}:")
    jual_semua_drone()

    # Coba beli drone dengan refresh berbayar
    drone_beli_siklus = 0
    refresh_siklus = 0

    while poin_tersisa >= (biaya_refresh + harga_drone):  # Minimal bisa beli 1 drone
        # Bayar refresh
        poin_tersisa -= biaya_refresh
        total_poin_beli_refresh += biaya_refresh
        refresh_siklus += 1

        drone_tersedia = 3  # Setiap refresh berbayar menyediakan 3 drone
        print(f"  Refresh berbayar {refresh_siklus}: Bayar refresh (-{biaya_refresh}), sisa: {poin_tersisa}")

        # Beli drone dari refresh sampai habis atau poin tidak cukup
        drone_tersisa = beli_dari_refresh(drone_tersedia, f"    Beli dari refresh {refresh_siklus}")
        drone_beli_refresh = 3 - drone_tersisa
        drone_beli_siklus += drone_beli_refresh

        # Jika masih ada drone tersisa di refresh dan poin tidak cukup, jual drone
        while drone_tersisa > 0 and poin_tersisa < harga_drone and total_drone_dibeli > 0:
            jual_semua_drone()
            if poin_tersisa >= harga_drone:
                drone_tersisa_baru = beli_dari_refresh(drone_tersisa, f"    Beli lagi dari refresh {refresh_siklus} (setelah jual)")
                drone_beli_refresh += (drone_tersisa - drone_tersisa_baru)
                drone_beli_siklus += (drone_tersisa - drone_tersisa_baru)
                drone_tersisa = drone_tersisa_baru

    if drone_beli_siklus > 0:
        print(f"  Total beli {drone_beli_siklus} drone dengan {refresh_siklus} refresh berbayar, sisa: {poin_tersisa}")
        siklus += 1
    else:
        print(f"  Tidak bisa beli drone lagi (poin tidak cukup untuk refresh + 1 drone)")
        break

print(f"\n=== RINGKASAN SIMULASI LENGKAP ===")
print(f"Total drone dibeli: {total_drone_dibeli}")
print(f"Total drone dijual: {total_drone_dijual}")
print(f"Total poin beli drone: {total_poin_beli_drone}")
print(f"Total poin beli refresh: {total_poin_beli_refresh}")
print(f"Total poin jual drone: {total_poin_jual_drone}")
print(f"Sisa poin akhir: {poin_tersisa}")

# Set variabel untuk kompatibilitas dengan kode lama
drone_hari_1_6 = 0  # Akan dihitung ulang
drone_hari_7 = 0    # Akan dihitung ulang
total_drone_beli_jualbeli = 0  # Akan dihitung ulang

# Hitung total poin yang dipakai dari simulasi
total_poin_dipakai = total_poin_beli_drone + total_poin_beli_refresh

print("\n=== HASIL AKHIR SIMULASI ===")
print(f"Total poin yang dipakai: {total_poin_dipakai}")
print(f"  - Pembelian drone: {total_poin_beli_drone}")
print(f"  - Pembelian refresh: {total_poin_beli_refresh}")
print(f"Sisa poin akhir: {poin_tersisa}")
print(f"Total drone akhir di tangan: {total_drone_dibeli}")
print(f"Total drone yang pernah dijual: {total_drone_dijual}")

print("\n=== VERIFIKASI ===")
print(f"Poin awal: {poin_awal}")
print(f"Poin dipakai: {total_poin_dipakai}")
print(f"Poin dari penjualan: {total_poin_jual_drone}")
print(f"Sisa poin: {poin_tersisa}")
print(f"Balance check: {poin_awal} - {total_poin_dipakai} + {total_poin_jual_drone} = {poin_awal - total_poin_dipakai + total_poin_jual_drone} (should equal {poin_tersisa})")