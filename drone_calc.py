# Parameter dasar
poin_awal = 3230
harga_drone = 100
harga_jual_drone = 80
biaya_refresh = 10
drone_per_refresh = 3

# Hari 1-6: 3x free refresh per hari (3 refresh × 3 drone = 9 drone per hari)
drone_hari_1_6 = 6 * 9
poin_terpakai_hari_1_6 = drone_hari_1_6 * harga_drone  # tanpa biaya refresh

# Hari 7: 3x free refresh + refresh berbayar hingga poin habis
drone_hari_7 = 0
poin_tersisa = poin_awal - poin_terpakai_hari_1_6

# 3x free refresh di hari ke-7
refresh_gratis = 3
drone_hari_7 += refresh_gratis * drone_per_refresh
poin_tersisa -= refresh_gratis * drone_per_refresh * harga_drone

# Refresh berbayar setelah itu
while poin_tersisa >= (biaya_refresh + harga_drone):
    poin_tersisa -= biaya_refresh
    for _ in range(drone_per_refresh):
        if poin_tersisa >= harga_drone:
            poin_tersisa -= harga_drone
            drone_hari_7 += 1
        else:
            break

# Total drone awal
total_drone = drone_hari_1_6 + drone_hari_7
total_poin_terpakai = poin_awal - poin_tersisa

# Siklus jual beli
total_drone_beli_jualbeli = 0
total_refresh_bayar_jualbeli = 0

while total_drone > 0:
    poin_tersisa += total_drone * harga_jual_drone
    total_drone = 0
    refresh_dalam_siklus = 0
    drone_dibeli = 0

    while poin_tersisa >= (biaya_refresh + harga_drone):
        poin_tersisa -= biaya_refresh
        refresh_dalam_siklus += 1
        for _ in range(drone_per_refresh):
            if poin_tersisa >= harga_drone:
                poin_tersisa -= harga_drone
                drone_dibeli += 1
            else:
                break

    total_refresh_bayar_jualbeli += refresh_dalam_siklus * biaya_refresh
    total_drone_beli_jualbeli += drone_dibeli
    total_drone = drone_dibeli  # ulangi dari drone yang baru dibeli

# Total akhir
total_poin_dipakai = total_poin_terpakai + total_refresh_bayar_jualbeli

# Output dengan detail perhitungan
print("=== DETAIL PERHITUNGAN ===")
print(f"Poin awal: {poin_awal}")
print(f"Drone hari 1-6: {drone_hari_1_6} drone × {harga_drone} poin = {drone_hari_1_6 * harga_drone} poin")
print(f"Drone hari 7: {drone_hari_7} drone")
print(f"Total poin terpakai fase awal: {total_poin_terpakai}")
print(f"Total biaya refresh berbayar jual-beli: {total_refresh_bayar_jualbeli}")
print(f"Total drone dari jual-beli: {total_drone_beli_jualbeli}")

print("\n=== HASIL AKHIR ===")
print(f"Total poin yang dipakai: {total_poin_dipakai}")
print(f"Sisa poin akhir: {poin_tersisa}")
print(f"Total drone yang dibeli hari 1-6: {drone_hari_1_6}")
print(f"Total drone yang dibeli hari 7: {drone_hari_7}")
print(f"Total drone yang dibeli dari jual-beli: {total_drone_beli_jualbeli}")

print("\n=== VERIFIKASI ===")
print(f"Total poin dipakai = Poin terpakai fase awal + Biaya refresh jual-beli")
print(f"{total_poin_dipakai} = {total_poin_terpakai} + {total_refresh_bayar_jualbeli}")
print(f"Drone jual-beli × harga drone = {total_drone_beli_jualbeli} × {harga_drone} = {total_drone_beli_jualbeli * harga_drone}")
print(f"Tapi biaya refresh jual-beli = {total_refresh_bayar_jualbeli} (ini biaya tambahan untuk refresh)")