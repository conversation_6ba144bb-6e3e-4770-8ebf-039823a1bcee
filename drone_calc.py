# Parameter dasar
poin_awal = 3230
harga_drone = 100
harga_jual_drone = 80
biaya_refresh = 10
drone_per_refresh = 3

# Simulasi hari per hari dengan jual-beli otomatis
poin_tersisa = poin_awal
total_drone_dibeli = 0
total_poin_beli_drone = 0
total_poin_beli_refresh = 0
total_drone_dijual = 0
total_poin_jual_drone = 0

print("=== SIMULASI HARI PER HARI ===")

# Hari 1-6: 3x free refresh per hari
for hari in range(1, 7):
    print(f"\nHari {hari}:")
    print(f"  Poin awal hari: {poin_tersisa}")

    # 3 refresh gratis per hari
    for refresh in range(3):
        # Cek apakah bisa beli 3 drone
        biaya_3_drone = 3 * harga_drone
        if poin_tersisa >= biaya_3_drone:
            # Beli 3 drone
            poin_tersisa -= biaya_3_drone
            total_drone_dibeli += 3
            total_poin_beli_drone += biaya_3_drone
            print(f"    Refresh {refresh+1}: Beli 3 drone (-{biaya_3_drone} poin), sisa: {poin_tersisa}")
        else:
            # Tidak cukup poin, harus jual drone dulu
            if total_drone_dibeli > 0:
                # Jual semua drone yang ada
                poin_jual = total_drone_dibeli * harga_jual_drone
                total_poin_jual_drone += poin_jual
                total_drone_dijual += total_drone_dibeli
                poin_tersisa += poin_jual
                print(f"    Jual {total_drone_dibeli} drone (+{poin_jual} poin), sisa: {poin_tersisa}")
                total_drone_dibeli = 0

                # Coba beli lagi setelah jual
                if poin_tersisa >= biaya_3_drone:
                    poin_tersisa -= biaya_3_drone
                    total_drone_dibeli += 3
                    total_poin_beli_drone += biaya_3_drone
                    print(f"    Refresh {refresh+1}: Beli 3 drone (-{biaya_3_drone} poin), sisa: {poin_tersisa}")
                else:
                    print(f"    Refresh {refresh+1}: Tidak cukup poin untuk beli 3 drone")
            else:
                print(f"    Refresh {refresh+1}: Tidak cukup poin dan tidak ada drone untuk dijual")

# Hari 7: 3x free refresh + refresh berbayar
print(f"\nHari 7:")
print(f"  Poin awal hari: {poin_tersisa}")

# 3 refresh gratis di hari 7
for refresh in range(3):
    biaya_3_drone = 3 * harga_drone
    if poin_tersisa >= biaya_3_drone:
        poin_tersisa -= biaya_3_drone
        total_drone_dibeli += 3
        total_poin_beli_drone += biaya_3_drone
        print(f"    Refresh gratis {refresh+1}: Beli 3 drone (-{biaya_3_drone} poin), sisa: {poin_tersisa}")
    else:
        # Jual drone dulu jika perlu
        if total_drone_dibeli > 0:
            poin_jual = total_drone_dibeli * harga_jual_drone
            total_poin_jual_drone += poin_jual
            total_drone_dijual += total_drone_dibeli
            poin_tersisa += poin_jual
            print(f"    Jual {total_drone_dibeli} drone (+{poin_jual} poin), sisa: {poin_tersisa}")
            total_drone_dibeli = 0

            if poin_tersisa >= biaya_3_drone:
                poin_tersisa -= biaya_3_drone
                total_drone_dibeli += 3
                total_poin_beli_drone += biaya_3_drone
                print(f"    Refresh gratis {refresh+1}: Beli 3 drone (-{biaya_3_drone} poin), sisa: {poin_tersisa}")

# Refresh berbayar di hari 7
refresh_berbayar = 0
while poin_tersisa >= (biaya_refresh + harga_drone):  # Minimal bisa beli 1 drone
    # Bayar refresh
    poin_tersisa -= biaya_refresh
    total_poin_beli_refresh += biaya_refresh
    refresh_berbayar += 1

    # Beli drone sebanyak mungkin (maksimal 3 per refresh)
    drone_refresh_ini = 0
    for _ in range(drone_per_refresh):
        if poin_tersisa >= harga_drone:
            poin_tersisa -= harga_drone
            total_drone_dibeli += 1
            total_poin_beli_drone += harga_drone
            drone_refresh_ini += 1
        else:
            break

    print(f"    Refresh berbayar {refresh_berbayar}: Bayar refresh (-{biaya_refresh}), beli {drone_refresh_ini} drone (-{drone_refresh_ini * harga_drone}), sisa: {poin_tersisa}")

# Lanjutkan jual-beli sampai tidak bisa lagi
print(f"\n=== SIKLUS JUAL-BELI LANJUTAN ===")
siklus = 1

while total_drone_dibeli > 0:
    # Jual semua drone yang ada
    poin_jual = total_drone_dibeli * harga_jual_drone
    total_poin_jual_drone += poin_jual
    total_drone_dijual += total_drone_dibeli
    poin_tersisa += poin_jual
    print(f"Siklus {siklus}: Jual {total_drone_dibeli} drone (+{poin_jual} poin), sisa: {poin_tersisa}")
    total_drone_dibeli = 0

    # Coba beli drone dengan refresh berbayar
    drone_beli_siklus = 0
    refresh_siklus = 0

    while poin_tersisa >= (biaya_refresh + harga_drone):  # Minimal bisa beli 1 drone
        # Bayar refresh
        poin_tersisa -= biaya_refresh
        total_poin_beli_refresh += biaya_refresh
        refresh_siklus += 1

        # Beli drone sebanyak mungkin (maksimal 3 per refresh)
        drone_per_refresh_ini = 0
        for _ in range(drone_per_refresh):  # Maksimal 3 drone per refresh
            if poin_tersisa >= harga_drone:
                poin_tersisa -= harga_drone
                total_drone_dibeli += 1
                total_poin_beli_drone += harga_drone
                drone_beli_siklus += 1
                drone_per_refresh_ini += 1
            else:
                break

        print(f"    Refresh {refresh_siklus}: Beli {drone_per_refresh_ini} drone, sisa: {poin_tersisa}")

    if drone_beli_siklus > 0:
        print(f"  Beli {drone_beli_siklus} drone dengan {refresh_siklus} refresh berbayar, sisa: {poin_tersisa}")
        siklus += 1
    else:
        print(f"  Tidak bisa beli drone lagi (poin tidak cukup)")
        break

print(f"\n=== RINGKASAN SIMULASI LENGKAP ===")
print(f"Total drone dibeli: {total_drone_dibeli}")
print(f"Total drone dijual: {total_drone_dijual}")
print(f"Total poin beli drone: {total_poin_beli_drone}")
print(f"Total poin beli refresh: {total_poin_beli_refresh}")
print(f"Total poin jual drone: {total_poin_jual_drone}")
print(f"Sisa poin akhir: {poin_tersisa}")

# Set variabel untuk kompatibilitas dengan kode lama
drone_hari_1_6 = 0  # Akan dihitung ulang
drone_hari_7 = 0    # Akan dihitung ulang
total_drone_beli_jualbeli = 0  # Akan dihitung ulang

# Hitung total poin yang dipakai dari simulasi
total_poin_dipakai = total_poin_beli_drone + total_poin_beli_refresh

print("\n=== HASIL AKHIR SIMULASI ===")
print(f"Total poin yang dipakai: {total_poin_dipakai}")
print(f"  - Pembelian drone: {total_poin_beli_drone}")
print(f"  - Pembelian refresh: {total_poin_beli_refresh}")
print(f"Sisa poin akhir: {poin_tersisa}")
print(f"Total drone akhir di tangan: {total_drone_dibeli}")
print(f"Total drone yang pernah dijual: {total_drone_dijual}")

print("\n=== VERIFIKASI ===")
print(f"Poin awal: {poin_awal}")
print(f"Poin dipakai: {total_poin_dipakai}")
print(f"Poin dari penjualan: {total_poin_jual_drone}")
print(f"Sisa poin: {poin_tersisa}")
print(f"Balance check: {poin_awal} - {total_poin_dipakai} + {total_poin_jual_drone} = {poin_awal - total_poin_dipakai + total_poin_jual_drone} (should equal {poin_tersisa})")